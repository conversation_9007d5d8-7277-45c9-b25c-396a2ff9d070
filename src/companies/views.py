import random
import string
from ast import List

from django.contrib.auth.hashers import make_password
from django.db.models import Q, TextField
from django.db.models.functions import Cast
from rest_framework import permissions, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import ValidationError, status
from rest_framework.response import Response

from authentication.models import User, UserStatusType
from cms.cms import get_cms_list, cms_filter
from cms.models import CmsUserKanna
from cms.serializers import CmsListSerializer
from cms.services import update_user_assign_cms, get_cms_customize
from diagnostics.models import Diagnostic
from kanna_api.models.user_kanna import UserKanna
from kanna_api.query_params.cms import CmsParam
from prefectures.models import Prefectures
from templates.emails.mailer import send_approve_email
from users.models import RequestOwner, RequestOwnerStatus, UserConstruction
from users.serializers import (
    RequestOwnerSerializer,
    UserKannaSerializer,
    UserSerializer,
)
from utilities.exceptions.api_exception import UserConstuctionError
from utilities.json_convert.convert import camel_to_snake_json
from utilities.permissions.custom_permission import IsKannaAdminPermission
from .models import Company, CompanyType, ShopDealer
from .serializers import (
    CompanyDropdownSerializer,
    CompanySerializer,
    DealerUpdateSerializer,
    ShopDealerInformationSerializer,
    ShopperDealerCreateSerializer,
    ShopperUpdateSerializer,
)


class CompanyViewSet(viewsets.ModelViewSet):
    serializer_class = CompanySerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]

    def get_serializer_class(self):
        if self.action in ["list", "retrieve"]:
            return ShopDealerInformationSerializer

        return CompanySerializer

    def get_queryset(self):
        def sort_prefecture_name(company: Company):
            try:
                _prefectures_id = company.prefectures_id

                return Prefectures.objects.get(pk=_prefectures_id).name
            except Exception:
                return ""

        def sort_dealer_name(company: Company):
            try:
                _shop = company.shop.all()

                return _shop.first().dealer.name
            except Exception:
                return ""

        query_params = self.request.query_params
        queryset = Company.objects.all()

        keyword = query_params.get("keyword", None)
        if keyword is not None:
            queryset = queryset.filter(Q(name__icontains=keyword))

        is_active = query_params.get("is_active", None)
        if is_active is None:
            is_active = True
        queryset = queryset.filter(Q(is_active=is_active))

        prefectures_id = query_params.get("prefectures_id", None)
        if prefectures_id is not None:
            queryset = queryset.filter(prefectures_id=prefectures_id)

        if query_params.get("is_companies", None):
            queryset = queryset.filter(type__in=[CompanyType.SHOP, CompanyType.DEALER])
            cpn_type = int(query_params.get("type", 0))
            if cpn_type and cpn_type in [3, 4]:
                company_type = CompanyType.SHOP
                if cpn_type == 3:
                    company_type = CompanyType.DEALER
                queryset = queryset.filter(type=company_type)
        else:
            cpn_type = query_params.get("type", None)
            if cpn_type:
                queryset = queryset.filter(type=cpn_type.upper())

        sort_by: str = query_params.get("sort_by", "")

        order_by_direction = query_params.get("order_by_direction", "ASC")
        sort_type = "-" if order_by_direction == "DESC" else ""

        if sort_by in ["type", "name"]:
            queryset = queryset.order_by(f"{sort_type}{sort_by}")

        if sort_by.startswith("prefectures"):
            queryset = sorted(
                queryset, key=sort_prefecture_name, reverse=bool(sort_type == "-")
            )

        if sort_by == "dealer":
            queryset = sorted(
                queryset, key=sort_dealer_name, reverse=bool(sort_type == "-")
            )

        return queryset

    def create(self, request, *args, **kwargs):
        serializer = ShopperDealerCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def update(self, request, *args, **kwargs):
        data = request.data
        company_type = data.pop("type")

        instance = Company.objects.filter(pk=kwargs["pk"], is_active=True).first()

        if company_type == CompanyType.DEALER:
            serializer = DealerUpdateSerializer(instance, data=request.data)
            serializer.is_valid(raise_exception=True)
            serializer.save()

        if company_type == CompanyType.SHOP:
            serializer = ShopperUpdateSerializer(
                instance, data=request.data, context={"shop_id": kwargs["pk"]}
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()

        return super().update(request, *args, **kwargs)

    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(self, request, *args, **kwargs)

    @action(
        detail=True,
        methods=["get"],
        permission_classes=[permissions.IsAuthenticated],
        url_name="members",
    )
    def members(self, request, **kwargs):
        company = Company.objects.filter(pk=kwargs["pk"], is_active=True).first()
        if company is None:
            raise ValidationError("Invalid company.")
        if request.method == "GET":
            members = UserKanna.objects.filter(company_id=company.id)
            page = self.paginate_queryset(members)
            if page is not None:
                serialize = UserKannaSerializer(
                    page, many=True, context={"request": request}
                )
                return self.get_paginated_response(serialize.data)
            return Response(
                UserKannaSerializer(
                    members, many=True, context={"request": request}
                ).data,
                200,
            )

        return Response(status=200)

    @action(
        detail=True,
        methods=["get"],
        # permission_classes=[permissions.IsAuthenticated],
        url_name="request_owners",
    )
    def request_owners(self, request, **kwargs):
        company = Company.objects.filter(pk=kwargs["pk"], is_active=True).first()
        if company is None:
            raise ValidationError("Company does not exitst.")
        if request.method == "GET":
            request_owners = RequestOwner.objects.filter(company_id=company.id)
            keyword = request.query_params.get("keyword", None)
            if keyword is not None:
                request_owners = request_owners.filter(
                    Q(first_name__icontains=keyword) | Q(last_name__icontains=keyword)
                )
            email = request.query_params.get("email", None)
            if email is not None:
                request_owners = request_owners.filter(Q(email__icontains=email))
            status = request.query_params.get("status", None)
            if status is not None:
                request_owners = request_owners.filter(Q(status=status))

            page = self.paginate_queryset(request_owners)
            if page is not None:
                serialize = RequestOwnerSerializer(
                    page, many=True, context={"request": request}
                )
                return self.get_paginated_response(serialize.data)
            else:
                serialize = RequestOwnerSerializer(
                    request_owners, many=True, context={"request": request}
                )
                return Response(
                    serialize.data,
                    200,
                )

    @action(
        detail=True,
        methods=["get"],
        permission_classes=[permissions.IsAuthenticated],
        url_name="owners",
    )
    def owners(self, request, **kwargs):
        company = Company.objects.filter(pk=kwargs["pk"]).first()
        if company is None:
            raise ValidationError("Company not exist.")
        users = User.objects.filter(company_id=company.pk, is_active=True)
        keyword = request.query_params.get("keyword", None)
        if keyword is not None:
            user_ids = []
            keyword = keyword.lower()
            for user in users:
                full_name = user.get_full_name()
                full_name_reverse = f"{user.first_name} {user.last_name}"
                first_name = user.first_name if user.first_name else ""
                last_name = user.last_name if user.last_name else ""
                if (
                    keyword in first_name.lower()
                    or keyword in last_name.lower()
                    or keyword in full_name.lower()
                    or keyword in full_name_reverse.lower()
                    or keyword.replace(" ", "") in full_name.lower().replace(" ", "")
                    or keyword.replace(" ", "")
                    in full_name_reverse.lower().replace(" ", "")
                ):
                    user_ids.append(user.id)
            users = users.filter(Q(id__in=user_ids))

        email = request.query_params.get("email", None)
        if email is not None:
            user_ids = []
            # users = users.filter(Q(email__icontains=email))
            for user in users:
                user_email = user.email
                if email in user_email:
                    user_ids.append(user.id)
            users = users.filter(Q(id__in=user_ids))
        is_active = request.query_params.get("is_active", None)
        if is_active is not None:
            users = users.filter(Q(is_active=is_active.capitalize()))
        status = request.query_params.get("status", None)
        if status is not None:
            users = users.filter(Q(status=status))

        page = self.paginate_queryset(users)

        if page is not None:
            serialize = UserSerializer(page, many=True, context={"request": request})

            return self.get_paginated_response(serialize.data)
        else:
            serialize = UserSerializer(users, many=True, context={"request": request})
            return Response(
                serialize.data,
                200,
            )

    # This function is used for approve owner join a company
    @action(
        detail=True,
        methods=["post"],
        permission_classes=[permissions.IsAuthenticated],
        url_name="approve",
    )
    def approve(self, request, **kwargs):
        company = self.get_object()
        # check company is exist
        request_data = request.data
        owner_id = request_data["owner_id"]
        if owner_id is None:
            return Response(data={"detail": "Please enter the owner id"}, status=400)

        try:
            request_owner = RequestOwner.objects.get(id=owner_id)
        except RequestOwner.DoesNotExist:
            return Response(data={"detail": "Owners is wrong"}, status=404)

        if request_owner.status == RequestOwnerStatus.APPROVED:
            return Response(200)
        if request_owner.status == RequestOwnerStatus.REJECT:
            return Response(data={"message": "すでにデータは更新されています"}, status=400)

        request_owner.company_id = company.pk
        request_owner.address = request_data["address"]
        request_owner.address_detail = request_data["address_detail"]
        request_owner.postcode = request_data["postcode"]
        request_owner.prefecture = request_data["prefecture"]
        request_owner.email = request_data["email"]
        request_owner.save()

        # TODO : Complete construction_ids
        gen_password = "".join(
            random.choices(string.ascii_uppercase + string.digits, k=8)
        )

        if request_owner.email:
            user_approve = User.objects.filter(email=request_owner.email).first()

            if user_approve:
                return Response(
                    data={
                        "message": "このメールアドレスはAGCシステムに既に存在しています。",
                    },
                    status=400,
                )
        else:

            return Response(
                data={
                    "message": "ここのメールアドレスはNullになっています。",
                },
                status=400,
            )

        user = User.objects.create(
            first_name=request_owner.first_name,
            last_name=request_owner.last_name,
            email=request_owner.email,
            password=make_password(gen_password),
            company_id=request_owner.company_id,
            phone=request_owner.phone,
            address=request_owner.address,
            address_detail=request_owner.address_detail,
            postcode=request_owner.postcode,
            prefecture=request_owner.prefecture,
            notes=request_owner.notes,
            status=UserStatusType.APPROVED,
            prefectures_id=request_owner.prefectures_id,
            is_verified=True,
        )
        user.save()

        request_owner.status = RequestOwnerStatus.APPROVED
        request_owner.save()

        construction_uuids = request_data["construction_uuids"]
        arr_user_construction: List[UserConstruction] = []
        if len(construction_uuids) == 0:
            construction_uuid_use_in_user = UserConstruction.objects.filter(
                owner_id=user.pk
            )
            if construction_uuid_use_in_user:
                construction_uuid_use_in_user.delete()
        else:
            construction_uuids = construction_uuids.split(",")
            for construction_uuid in construction_uuids:

                construction_uuid_used = UserConstruction.objects.filter(
                    ~Q(owner_id=user.pk), construction_uuid=construction_uuid
                )

                if not construction_uuid_used:

                    item = UserConstruction(
                        **{
                            "owner_id": user.pk,
                            "construction_uuid": construction_uuid,
                        }
                    )
                    arr_user_construction.append(item)
                else:
                    raise UserConstuctionError(
                        {
                            "owner_id": user.pk,
                            "construction_uuid": construction_uuid,
                            "detail": "construction was used",
                        },
                    )

            construction_uuid_use_in_user = UserConstruction.objects.filter(
                owner_id=user.pk
            )

            if construction_uuid_use_in_user:
                construction_uuid_use_in_user.delete()

            UserConstruction.objects.bulk_create(arr_user_construction)

        # send mail notify owner is active
        send_approve_email(user, company, gen_password)
        return Response(200)

    # This function is used for reject owner join a company
    @action(
        detail=True,
        methods=["post"],
        permission_classes=[permissions.IsAuthenticated],
        url_name="reject",
    )
    def reject(self, request, **kwargs):
        company = self.get_object()

        owner_id = request.data.get("owner_ids", None)
        if owner_id is None:
            return Response(data={"detail": "Please enter the owner ids"}, status=400)

        try:
            request_owner = RequestOwner.objects.get(id=owner_id)
        except RequestOwner.DoesNotExist:
            return Response(data={"detail": "Owners is wrong"}, status=400)

        if request_owner.status == RequestOwnerStatus.REJECT:
            return Response(200)
        if request_owner.status == RequestOwnerStatus.APPROVED:
            return Response(data={"message": "すでにデータは更新されています"}, status=400)

        request_owner.status = RequestOwnerStatus.REJECT
        request_owner.company_id = company.pk
        request_owner.save()
        return Response(200)

    @action(
        detail=True,
        methods=["get"],
        permission_classes=[permissions.IsAuthenticated],
        url_name="dealer_by_shop",
    )
    def dealer_by_shop(self, request, **kwargs):

        shop_dealer = ShopDealer.objects.filter(shop_id=kwargs["pk"]).first()
        company = []
        if shop_dealer:
            company = Company.objects.filter(id=shop_dealer.dealer_id)

        return Response(
            CompanySerializer(company, many=True, context={"request": request}).data,
            200,
        )

    @action(
        detail=False,
        methods=["get"],
        url_name="companies_drop_down",
    )
    def companies_drop_down(self, request, **kwargs):
        company_type = request.GET.get("type")
        list_companies = Company.objects.all()

        if company_type in [CompanyType.DEALER, CompanyType.SHOP]:
            list_companies = list_companies.filter(type=company_type)

        return Response(
            CompanyDropdownSerializer(
                list_companies, many=True, context={"request": request}
            ).data,
            200,
        )

    @action(
        detail=False,
        methods=["get"],
        permission_classes=[IsKannaAdminPermission],
        url_name="list_owners",
    )
    def list_owners(self, request, **kwargs):
        def normalize_str(s):
            return (s or "").lower().replace(" ", "")

        def is_keyword_in_user(kw, user):
            kw = normalize_str(kw)
            first_name = normalize_str(user.first_name)
            last_name = normalize_str(user.last_name)
            full_name = normalize_str(user.get_full_name())
            reversed_full_name = f"{first_name}{last_name}"
            return (kw in first_name or
                    kw in last_name or
                    kw in full_name or
                    kw in reversed_full_name)

        users = User.objects.filter(company_id__isnull=False, is_active=True)
        query_params = request.query_params

        # Filter encrypted fields
        if keyword := query_params.get("keyword"):
            user_ids = set()
            for user in users:
                if is_keyword_in_user(keyword, user):
                    user_ids.add(user.id)
            users = users.filter(Q(id__in=user_ids))

        if email := query_params.get("email"):
            user_ids = set()
            for user in users:
                if email in user.email:
                    user_ids.add(user.id)
            users = users.filter(Q(id__in=user_ids))

        filter_condition = Q()
        if company_id := query_params.get("company_id", None):
            filter_condition &= Q(company_id=company_id)

        if status := query_params.get("status", None):
            filter_condition &= Q(status=status)

        users = users.filter(filter_condition)

        page = self.paginate_queryset(users)
        if page is not None:
            serialize = UserSerializer(page, many=True, context={"request": request})
            return self.get_paginated_response(serialize.data)

        else:
            serialize = UserSerializer(users, many=True, context={"request": request})
            return Response(
                serialize.data,
                200,
            )

    @action(
        detail=False,
        methods=["get"],
        permission_classes=[IsKannaAdminPermission],
        url_name="list_request_owners",
    )
    def list_request_owners(self, request, **kwargs):
        query_params = request.query_params
        filter_condition = Q(status=RequestOwnerStatus.WAITING_FOR_APPROVE)

        company_id = query_params.get("company_id", None)
        if company_id is not None:
            filter_condition &= Q(company_id=company_id)

        keyword = query_params.get("keyword", None)
        if keyword is not None:
            filter_condition &= Q(
                Q(first_name__icontains=keyword) | Q(last_name__icontains=keyword)
            )

        email = query_params.get("email", None)
        if email is not None:
            filter_condition &= Q(email__icontains=email)

        request_owners_queryset = RequestOwner.objects.filter(filter_condition)

        page = self.paginate_queryset(request_owners_queryset)
        if page is not None:
            serialize = RequestOwnerSerializer(
                page, many=True, context={"request": request}
            )
            return self.get_paginated_response(serialize.data)
        else:
            serialize = RequestOwnerSerializer(
                request_owners_queryset, many=True, context={"request": request}
            )
            return Response(
                serialize.data,
                200,
            )


    @action(
        detail=True,
        methods=["get"],
        permission_classes=[IsKannaAdminPermission],
        url_name="construction_list",
    )
    def construction_list(self, request, *args, **kwargs):
        params = CmsParam(**request.query_params.dict())
        # cms取得
        kanna_user = UserKanna.objects.get(email=request.user.email)
        if kanna_user:
            update_user_assign_cms(kanna_user)

        cms_current = CmsUserKanna.objects.annotate(
            str=Cast("cms_uuid", output_field=TextField())
        ).values_list("str", flat=True)

        cms_uuid_list_filter = (
            Diagnostic.objects.annotate(
                str=Cast("cms_uuid", output_field=TextField())
            )
            .values_list("str", flat=True)
            .filter(
                str__in=list(cms_current), company_id=kwargs['pk']
            )
        )
        cms_list_data = []
        if cms_uuid_list_filter:

            cms_list = get_cms_list(user=kanna_user, cms_user_uuid=kanna_user.user_uuid)

            cms_list_data = camel_to_snake_json(cms_list)

            cms_list_data = cms_filter(
                request.user,
                params=params,
                cms_list_data=cms_list_data,
                on_user_cms_uuid_list=[],
                not_on_user_cms_uuid_list=[],
                not_on_group_cms_uuid_list=[],
                cms_uuid_list=list(cms_uuid_list_filter),
            )

            for cms in cms_list_data:
                get_cms_customize(cms)

        serializer = CmsListSerializer(cms_list_data, many=True)
        page = self.paginate_queryset(serializer.data)

        if page is not None:
            return self.get_paginated_response(serializer.data)
        return Response(
            serializer.data,
            200,
        )
